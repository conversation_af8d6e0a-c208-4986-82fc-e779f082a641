# Makefile for Raft-KV Integration Tests
# 🌟 Fiber + RPC + Raft 完美协同集成测试编译脚本

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
INCLUDES = -I../include -I../raftRpcPro
LIBS = -lprotobuf -lpthread -ldl

# 目录设置
SRC_DIR = .
BUILD_DIR = build
BIN_DIR = bin

# 源文件
INTEGRATION_TEST_SRC = integration_test.cpp
CLIENT_EXAMPLE_SRC = kv_client_example.cpp

# 目标文件
INTEGRATION_TEST_TARGET = $(BIN_DIR)/integration_test
CLIENT_EXAMPLE_TARGET = $(BIN_DIR)/kv_client_example

# protobuf生成的文件
PROTO_SRCS = ../raftRpcPro/kvServerRPC.pb.cc
PROTO_OBJS = $(BUILD_DIR)/kvServerRPC.pb.o

# 项目库文件（需要根据实际情况调整）
PROJECT_LIBS = -L../lib -lraft-kv

# 默认目标
.PHONY: all clean test help install

all: $(INTEGRATION_TEST_TARGET) $(CLIENT_EXAMPLE_TARGET)

# 创建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	@mkdir -p $(BIN_DIR)

# 编译protobuf文件
$(PROTO_OBJS): $(PROTO_SRCS) | $(BUILD_DIR)
	@echo "🔧 编译protobuf文件..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译集成测试
$(INTEGRATION_TEST_TARGET): $(INTEGRATION_TEST_SRC) $(PROTO_OBJS) | $(BIN_DIR)
	@echo "🚀 编译集成测试..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(PROTO_OBJS) $(PROJECT_LIBS) $(LIBS)
	@echo "✅ 集成测试编译完成: $@"

# 编译客户端示例
$(CLIENT_EXAMPLE_TARGET): $(CLIENT_EXAMPLE_SRC) $(PROTO_OBJS) | $(BIN_DIR)
	@echo "🔧 编译客户端示例..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(PROTO_OBJS) $(PROJECT_LIBS) $(LIBS)
	@echo "✅ 客户端示例编译完成: $@"

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	rm -rf $(BUILD_DIR) $(BIN_DIR)
	rm -f integration_test_config.txt
	@echo "✅ 清理完成"

# 运行基本测试
test: $(INTEGRATION_TEST_TARGET)
	@echo "🧪 运行基本集成测试..."
	./$(INTEGRATION_TEST_TARGET) 3 30

# 运行性能测试
test-perf: $(INTEGRATION_TEST_TARGET)
	@echo "⚡ 运行性能测试..."
	./$(INTEGRATION_TEST_TARGET) 3 60 perf

# 运行故障测试
test-fault: $(INTEGRATION_TEST_TARGET)
	@echo "💥 运行故障测试..."
	./$(INTEGRATION_TEST_TARGET) 5 60 perf fault

# 运行完整测试套件
test-full: $(INTEGRATION_TEST_TARGET)
	@echo "🎯 运行完整测试套件..."
	./$(INTEGRATION_TEST_TARGET) 5 300 perf fault

# 运行客户端示例
demo: $(CLIENT_EXAMPLE_TARGET)
	@echo "🎮 运行客户端示例..."
	./$(CLIENT_EXAMPLE_TARGET)

# 安装到系统目录（可选）
install: all
	@echo "📦 安装测试程序..."
	sudo cp $(INTEGRATION_TEST_TARGET) /usr/local/bin/
	sudo cp $(CLIENT_EXAMPLE_TARGET) /usr/local/bin/
	@echo "✅ 安装完成"

# 检查依赖
check-deps:
	@echo "🔍 检查编译依赖..."
	@which $(CXX) > /dev/null || (echo "❌ 未找到C++编译器"; exit 1)
	@pkg-config --exists protobuf || (echo "❌ 未找到protobuf库"; exit 1)
	@echo "✅ 依赖检查通过"

# 显示帮助信息
help:
	@echo "🌟 ========================================== 🌟"
	@echo "🚀    Raft-KV 集成测试编译脚本帮助          🚀"
	@echo "🌟 ========================================== 🌟"
	@echo ""
	@echo "📋 可用目标："
	@echo "  all          - 编译所有程序"
	@echo "  clean        - 清理编译文件"
	@echo "  test         - 运行基本测试 (3节点, 30秒)"
	@echo "  test-perf    - 运行性能测试 (3节点, 60秒)"
	@echo "  test-fault   - 运行故障测试 (5节点, 60秒)"
	@echo "  test-full    - 运行完整测试 (5节点, 300秒)"
	@echo "  demo         - 运行客户端示例"
	@echo "  install      - 安装到系统目录"
	@echo "  check-deps   - 检查编译依赖"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "🎯 使用示例："
	@echo "  make all           # 编译所有程序"
	@echo "  make test          # 运行基本测试"
	@echo "  make test-full     # 运行完整测试套件"
	@echo "  make demo          # 运行客户端示例"
	@echo ""
	@echo "🔧 自定义运行："
	@echo "  ./bin/integration_test [节点数] [时长] [perf] [fault]"
	@echo "  ./bin/kv_client_example"
	@echo ""
	@echo "🌟 ========================================== 🌟"

# 调试版本
debug: CXXFLAGS += -DDEBUG -O0
debug: $(INTEGRATION_TEST_TARGET) $(CLIENT_EXAMPLE_TARGET)

# 发布版本
release: CXXFLAGS += -DNDEBUG -O3
release: $(INTEGRATION_TEST_TARGET) $(CLIENT_EXAMPLE_TARGET)

# 内存检查版本（需要valgrind）
memcheck: debug
	@echo "🔍 运行内存检查..."
	valgrind --leak-check=full --show-leak-kinds=all ./$(INTEGRATION_TEST_TARGET) 3 10

# 性能分析版本（需要gprof）
profile: CXXFLAGS += -pg
profile: $(INTEGRATION_TEST_TARGET)
	@echo "📊 运行性能分析..."
	./$(INTEGRATION_TEST_TARGET) 3 30
	gprof ./$(INTEGRATION_TEST_TARGET) gmon.out > profile_report.txt
	@echo "✅ 性能分析报告已生成: profile_report.txt"

# 代码覆盖率测试（需要gcov）
coverage: CXXFLAGS += --coverage
coverage: LIBS += --coverage
coverage: $(INTEGRATION_TEST_TARGET)
	@echo "📈 运行代码覆盖率测试..."
	./$(INTEGRATION_TEST_TARGET) 3 10
	gcov $(INTEGRATION_TEST_SRC)
	@echo "✅ 代码覆盖率报告已生成"

# 静态分析（需要cppcheck）
static-analysis:
	@echo "🔍 运行静态代码分析..."
	cppcheck --enable=all --std=c++17 $(INTEGRATION_TEST_SRC) $(CLIENT_EXAMPLE_SRC)

# 格式化代码（需要clang-format）
format:
	@echo "🎨 格式化代码..."
	clang-format -i $(INTEGRATION_TEST_SRC) $(CLIENT_EXAMPLE_SRC)
	@echo "✅ 代码格式化完成"

# 显示编译信息
info:
	@echo "🔧 编译配置信息："
	@echo "  编译器: $(CXX)"
	@echo "  编译选项: $(CXXFLAGS)"
	@echo "  包含目录: $(INCLUDES)"
	@echo "  链接库: $(LIBS)"
	@echo "  项目库: $(PROJECT_LIBS)"

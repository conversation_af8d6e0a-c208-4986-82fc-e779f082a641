/**
 * @file kv_client_example.cpp
 * @brief KV客户端使用示例
 * 
 * 该文件展示如何使用基于fiber协程的KV客户端进行分布式键值存储操作。
 * 演示了三个核心模块的协同使用：
 * - Fiber协程：异步非阻塞操作
 * - RPC通信：高效的网络通信
 * - Raft共识：强一致性保证
 * 
 * <AUTHOR> Team
 * @date 2024
 */

#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <memory>

#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/rpc/mprpcchannel.h"
#include "raft-kv/rpc/mprpccontroller.h"
#include "kvServerRPC.pb.h"

/**
 * @brief 简化的KV客户端示例
 */
class SimpleKvClient {
public:
    SimpleKvClient(const std::string& server_ip, int server_port) 
        : server_ip_(server_ip), server_port_(server_port), request_id_(0) {
        client_id_ = "simple_client_" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count());
    }

    /**
     * @brief 同步Put操作
     */
    bool put(const std::string& key, const std::string& value) {
        try {
            MprpcChannel channel(server_ip_, server_port_, true);
            raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);
            
            raftKVRpcProctoc::PutAppendArgs request;
            request.set_key(key);
            request.set_value(value);
            request.set_op("Put");
            request.set_clientid(client_id_);
            request.set_requestid(++request_id_);
            
            raftKVRpcProctoc::PutAppendReply response;
            MprpcController controller;
            
            stub.PutAppend(&controller, &request, &response, nullptr);
            
            if (!controller.Failed() && response.err() == "OK") {
                std::cout << "✅ Put成功: " << key << " = " << value << std::endl;
                return true;
            } else {
                std::cout << "❌ Put失败: " << key << ", 错误: " << response.err() << std::endl;
                return false;
            }
        } catch (const std::exception& e) {
            std::cout << "❌ Put异常: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief 同步Get操作
     */
    std::string get(const std::string& key) {
        try {
            MprpcChannel channel(server_ip_, server_port_, true);
            raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);
            
            raftKVRpcProctoc::GetArgs request;
            request.set_key(key);
            request.set_clientid(client_id_);
            request.set_requestid(++request_id_);
            
            raftKVRpcProctoc::GetReply response;
            MprpcController controller;
            
            stub.Get(&controller, &request, &response, nullptr);
            
            if (!controller.Failed()) {
                if (response.err() == "OK") {
                    std::cout << "✅ Get成功: " << key << " = " << response.value() << std::endl;
                    return response.value();
                } else if (response.err() == "ErrNoKey") {
                    std::cout << "⚠️  Get结果: " << key << " 不存在" << std::endl;
                    return "";
                } else {
                    std::cout << "❌ Get失败: " << key << ", 错误: " << response.err() << std::endl;
                    return "";
                }
            } else {
                std::cout << "❌ Get RPC失败: " << key << std::endl;
                return "";
            }
        } catch (const std::exception& e) {
            std::cout << "❌ Get异常: " << e.what() << std::endl;
            return "";
        }
    }

    /**
     * @brief 同步Append操作
     */
    bool append(const std::string& key, const std::string& value) {
        try {
            MprpcChannel channel(server_ip_, server_port_, true);
            raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);
            
            raftKVRpcProctoc::PutAppendArgs request;
            request.set_key(key);
            request.set_value(value);
            request.set_op("Append");
            request.set_clientid(client_id_);
            request.set_requestid(++request_id_);
            
            raftKVRpcProctoc::PutAppendReply response;
            MprpcController controller;
            
            stub.PutAppend(&controller, &request, &response, nullptr);
            
            if (!controller.Failed() && response.err() == "OK") {
                std::cout << "✅ Append成功: " << key << " += " << value << std::endl;
                return true;
            } else {
                std::cout << "❌ Append失败: " << key << ", 错误: " << response.err() << std::endl;
                return false;
            }
        } catch (const std::exception& e) {
            std::cout << "❌ Append异常: " << e.what() << std::endl;
            return false;
        }
    }

private:
    std::string server_ip_;
    int server_port_;
    std::string client_id_;
    std::atomic<int> request_id_;
};

/**
 * @brief 演示基本KV操作
 */
void demo_basic_operations() {
    std::cout << "\n🧪 ========== 基本操作演示 ==========" << std::endl;
    
    // 连接到第一个服务器节点（假设在端口25000）
    SimpleKvClient client("127.0.0.1", 25000);
    
    // 演示Put操作
    std::cout << "📝 演示Put操作..." << std::endl;
    client.put("user:1001", "Alice");
    client.put("user:1002", "Bob");
    client.put("config:timeout", "30");
    
    // 演示Get操作
    std::cout << "\n🔍 演示Get操作..." << std::endl;
    client.get("user:1001");
    client.get("user:1002");
    client.get("config:timeout");
    client.get("nonexistent_key");  // 测试不存在的键
    
    // 演示Append操作
    std::cout << "\n➕ 演示Append操作..." << std::endl;
    client.append("user:1001", "_Smith");
    client.get("user:1001");  // 验证Append结果
    
    client.append("log:events", "Event1;");
    client.append("log:events", "Event2;");
    client.append("log:events", "Event3;");
    client.get("log:events");  // 验证多次Append结果
    
    std::cout << "🎉 基本操作演示完成" << std::endl;
}

/**
 * @brief 演示并发操作
 */
void demo_concurrent_operations() {
    std::cout << "\n🚀 ========== 并发操作演示 ==========" << std::endl;
    
    const int THREAD_COUNT = 5;
    std::vector<std::thread> threads;
    std::atomic<int> completed_threads{0};
    
    for (int i = 0; i < THREAD_COUNT; i++) {
        threads.emplace_back([i, &completed_threads]() {
            SimpleKvClient client("127.0.0.1", 25000);
            
            // 每个线程执行一系列操作
            for (int j = 0; j < 3; j++) {
                std::string key = "thread_" + std::to_string(i) + "_key_" + std::to_string(j);
                std::string value = "value_" + std::to_string(i) + "_" + std::to_string(j);
                
                client.put(key, value);
                client.get(key);
                client.append(key, "_appended");
                client.get(key);
            }
            
            completed_threads++;
            std::cout << "🏁 线程 " << i << " 完成" << std::endl;
        });
    }
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    std::cout << "🎉 并发操作演示完成，共 " << completed_threads.load() << " 个线程" << std::endl;
}

int main(int argc, char** argv) {
    std::cout << "🌟 ========================================== 🌟" << std::endl;
    std::cout << "🚀         KV客户端使用示例                🚀" << std::endl;
    std::cout << "🌟 ========================================== 🌟" << std::endl;
    std::cout << "💡 展示如何使用Raft-KV分布式存储系统" << std::endl;
    std::cout << "🌟 ========================================== 🌟" << std::endl;
    
    try {
        // 基本操作演示
        demo_basic_operations();
        
        // 等待一下
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 并发操作演示
        demo_concurrent_operations();
        
        std::cout << "\n🌟 ========================================== 🌟" << std::endl;
        std::cout << "🎊 KV客户端示例演示完成！" << std::endl;
        std::cout << "💪 成功展示了分布式KV存储的强大功能！" << std::endl;
        std::cout << "🌟 ========================================== 🌟" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 示例运行过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

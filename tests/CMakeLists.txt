# CMakeLists.txt for Raft-KV Integration Tests
# 🌟 Fiber + RPC + Raft 完美协同集成测试 CMake 配置

cmake_minimum_required(VERSION 3.16)
project(RaftKV_IntegrationTests VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# 如果没有指定构建类型，默认为Release
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

message(STATUS "🚀 构建类型: ${CMAKE_BUILD_TYPE}")

# 查找依赖包
find_package(Protobuf REQUIRED)
find_package(Threads REQUIRED)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro
    ${CMAKE_CURRENT_SOURCE_DIR}/../src
    ${Protobuf_INCLUDE_DIRS}
)

# 链接目录
link_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../build
)

# protobuf生成的源文件
set(PROTO_SRCS
    ${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro/kvServerRPC.pb.cc
)

# 检查protobuf文件是否存在
if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro/kvServerRPC.pb.cc)
    message(STATUS "🔧 protobuf文件不存在，尝试生成...")
    
    # 查找protoc编译器
    find_program(PROTOC_EXECUTABLE protoc)
    if(PROTOC_EXECUTABLE)
        execute_process(
            COMMAND ${PROTOC_EXECUTABLE} 
                --cpp_out=${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro
                --proto_path=${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro
                ${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro/kvServerRPC.proto
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            RESULT_VARIABLE PROTOC_RESULT
        )
        
        if(PROTOC_RESULT EQUAL 0)
            message(STATUS "✅ protobuf文件生成成功")
        else()
            message(WARNING "⚠️  protobuf文件生成失败")
        endif()
    else()
        message(WARNING "⚠️  未找到protoc编译器")
    endif()
endif()

# 创建protobuf库
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/../raftRpcPro/kvServerRPC.pb.cc)
    add_library(raft_kv_proto STATIC ${PROTO_SRCS})
    target_link_libraries(raft_kv_proto ${Protobuf_LIBRARIES})
    target_include_directories(raft_kv_proto PUBLIC ${Protobuf_INCLUDE_DIRS})
    
    message(STATUS "✅ protobuf库配置完成")
else()
    message(FATAL_ERROR "❌ 无法找到protobuf源文件")
endif()

# 查找项目库文件
set(PROJECT_LIBS "")

# 尝试查找各种可能的库文件
foreach(lib_name IN ITEMS 
    raft-kv 
    raftKV 
    libraft-kv.a 
    libraftKV.a
    monsoon
    fiber
    rpc
    raft)
    
    find_library(${lib_name}_LIB 
        NAMES ${lib_name}
        PATHS 
            ${CMAKE_CURRENT_SOURCE_DIR}/../lib
            ${CMAKE_CURRENT_SOURCE_DIR}/../build
            ${CMAKE_CURRENT_SOURCE_DIR}/../build/lib
        NO_DEFAULT_PATH
    )
    
    if(${lib_name}_LIB)
        list(APPEND PROJECT_LIBS ${${lib_name}_LIB})
        message(STATUS "✅ 找到库: ${${lib_name}_LIB}")
    endif()
endforeach()

# 如果没有找到预编译的库，尝试编译源文件
if(NOT PROJECT_LIBS)
    message(STATUS "🔧 未找到预编译库，尝试包含源文件...")
    
    # 查找源文件
    file(GLOB_RECURSE FIBER_SRCS 
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/fiber/*.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/fiber/*.cc
    )
    
    file(GLOB_RECURSE RPC_SRCS 
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/rpc/*.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/rpc/*.cc
    )
    
    file(GLOB_RECURSE RAFT_SRCS 
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/raftCore/*.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/raftCore/*.cc
    )
    
    set(ALL_PROJECT_SRCS ${FIBER_SRCS} ${RPC_SRCS} ${RAFT_SRCS})
    
    if(ALL_PROJECT_SRCS)
        add_library(raft_kv_combined STATIC ${ALL_PROJECT_SRCS})
        target_link_libraries(raft_kv_combined 
            ${Protobuf_LIBRARIES} 
            Threads::Threads
        )
        list(APPEND PROJECT_LIBS raft_kv_combined)
        message(STATUS "✅ 源文件库配置完成")
    endif()
endif()

# 集成测试可执行文件
add_executable(integration_test integration_test.cpp)

target_link_libraries(integration_test
    raft_kv_proto
    ${PROJECT_LIBS}
    ${Protobuf_LIBRARIES}
    Threads::Threads
    dl
)

# 客户端示例可执行文件
add_executable(kv_client_example kv_client_example.cpp)

target_link_libraries(kv_client_example
    raft_kv_proto
    ${PROJECT_LIBS}
    ${Protobuf_LIBRARIES}
    Threads::Threads
    dl
)

# 设置输出目录
set_target_properties(integration_test kv_client_example
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)

# 自定义目标：运行测试
add_custom_target(run_test
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/integration_test 3 30
    DEPENDS integration_test
    COMMENT "🧪 运行基本集成测试..."
)

add_custom_target(run_test_perf
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/integration_test 3 60 perf
    DEPENDS integration_test
    COMMENT "⚡ 运行性能测试..."
)

add_custom_target(run_test_fault
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/integration_test 5 60 perf fault
    DEPENDS integration_test
    COMMENT "💥 运行故障测试..."
)

add_custom_target(run_test_full
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/integration_test 5 300 perf fault
    DEPENDS integration_test
    COMMENT "🎯 运行完整测试套件..."
)

add_custom_target(run_demo
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/bin/kv_client_example
    DEPENDS kv_client_example
    COMMENT "🎮 运行客户端示例..."
)

# 清理自定义目标
add_custom_target(clean_test_files
    COMMAND ${CMAKE_COMMAND} -E remove -f integration_test_config.txt
    COMMENT "🧹 清理测试配置文件..."
)

# 安装目标
install(TARGETS integration_test kv_client_example
    RUNTIME DESTINATION bin
    COMPONENT tests
)

install(FILES README_INTEGRATION_TEST.md
    DESTINATION share/doc/raft-kv
    COMPONENT documentation
)

# 打印配置信息
message(STATUS "🌟 ========================================== 🌟")
message(STATUS "🚀    Raft-KV 集成测试 CMake 配置完成      🚀")
message(STATUS "🌟 ========================================== 🌟")
message(STATUS "📋 配置信息:")
message(STATUS "   构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "   C++标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "   编译器: ${CMAKE_CXX_COMPILER}")
message(STATUS "   Protobuf版本: ${Protobuf_VERSION}")
message(STATUS "   输出目录: ${CMAKE_CURRENT_BINARY_DIR}/bin")
message(STATUS "")
message(STATUS "🎯 构建命令:")
message(STATUS "   mkdir build && cd build")
message(STATUS "   cmake ..")
message(STATUS "   make -j$(nproc)")
message(STATUS "")
message(STATUS "🧪 测试命令:")
message(STATUS "   make run_test          # 基本测试")
message(STATUS "   make run_test_perf     # 性能测试")
message(STATUS "   make run_test_fault    # 故障测试")
message(STATUS "   make run_test_full     # 完整测试")
message(STATUS "   make run_demo          # 客户端示例")
message(STATUS "🌟 ========================================== 🌟")

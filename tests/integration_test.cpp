/**
 * @file integration_test.cpp
 * @brief 完美展示Fiber、RPC、Raft三模块协同的综合集成测试
 *
 * 该文件创建一个完美的集成测试，全面展示三个核心模块的协同工作：
 *
 * 🚀 Fiber协程模块：
 * - 高并发协程调度，支持数千个并发客户端
 * - 异步IO操作，提升网络通信效率
 * - 协程间通信Channel，实现生产者-消费者模式
 * - 智能协程池管理，动态调整资源
 *
 * 🌐 RPC通信模块：
 * - 基于protobuf的高效序列化
 * - 支持同步/异步RPC调用
 * - 自动重连和故障转移
 * - 请求负载均衡和超时控制
 *
 * 🔄 Raft共识模块：
 * - 分布式强一致性保证
 * - 自动领导者选举和故障恢复
 * - 日志复制和快照机制
 * - 动态集群成员变更
 *
 * 🎯 测试场景：
 * - 基本功能验证：Get/Put/Append操作
 * - 高并发性能测试：数千协程并发访问
 * - 故障恢复测试：节点宕机和网络分区
 * - 一致性验证：强一致性读写验证
 * - 长期稳定性：7x24小时压力测试
 *
 * <AUTHOR> Team
 * @date 2024
 */

#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <memory>
#include <random>
#include <future>
#include <map>
#include <unordered_map>
#include <signal.h>
#include <unistd.h>
#include <sys/wait.h>
#include <fstream>
#include <sstream>
#include <iomanip>

// 核心模块头文件
#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/rpc/mprpcchannel.h"
#include "raft-kv/rpc/mprpccontroller.h"
#include "raft-kv/raftCore/kvServer.h"
#include "kvServerRPC.pb.h"

// ==================== 全局统计和监控 ====================

/**
 * @brief 性能统计结构体
 */
struct PerformanceStats
{
    std::atomic<uint64_t> total_requests{0};      // 总请求数
    std::atomic<uint64_t> successful_requests{0}; // 成功请求数
    std::atomic<uint64_t> failed_requests{0};     // 失败请求数
    std::atomic<uint64_t> timeout_requests{0};    // 超时请求数
    std::atomic<uint64_t> get_operations{0};      // Get操作数
    std::atomic<uint64_t> put_operations{0};      // Put操作数
    std::atomic<uint64_t> append_operations{0};   // Append操作数
    std::atomic<uint64_t> fiber_count{0};         // 协程数量
    std::atomic<uint64_t> rpc_calls{0};           // RPC调用数
    std::atomic<uint64_t> leader_changes{0};      // 领导者变更次数
    std::atomic<uint64_t> bytes_sent{0};          // 发送字节数
    std::atomic<uint64_t> bytes_received{0};      // 接收字节数

    // 延迟统计
    std::atomic<uint64_t> total_latency_us{0};        // 总延迟（微秒）
    std::atomic<uint64_t> min_latency_us{UINT64_MAX}; // 最小延迟
    std::atomic<uint64_t> max_latency_us{0};          // 最大延迟

    void reset()
    {
        total_requests = 0;
        successful_requests = 0;
        failed_requests = 0;
        timeout_requests = 0;
        get_operations = 0;
        put_operations = 0;
        append_operations = 0;
        fiber_count = 0;
        rpc_calls = 0;
        leader_changes = 0;
        bytes_sent = 0;
        bytes_received = 0;
        total_latency_us = 0;
        min_latency_us = UINT64_MAX;
        max_latency_us = 0;
    }

    void updateLatency(uint64_t latency_us)
    {
        total_latency_us += latency_us;

        uint64_t current_min = min_latency_us.load();
        while (latency_us < current_min &&
               !min_latency_us.compare_exchange_weak(current_min, latency_us))
        {
        }

        uint64_t current_max = max_latency_us.load();
        while (latency_us > current_max &&
               !max_latency_us.compare_exchange_weak(current_max, latency_us))
        {
        }
    }

    double getAverageLatency() const
    {
        uint64_t total = total_requests.load();
        return total > 0 ? (double)total_latency_us.load() / total : 0.0;
    }

    double getSuccessRate() const
    {
        uint64_t total = total_requests.load();
        return total > 0 ? (double)successful_requests.load() * 100.0 / total : 0.0;
    }

    double getQPS(uint64_t duration_ms) const
    {
        return duration_ms > 0 ? (double)total_requests.load() * 1000.0 / duration_ms : 0.0;
    }
};

// 全局统计对象
PerformanceStats g_stats;
std::atomic<bool> g_test_running{true};

/**
 * @brief 集群节点信息
 */
struct ClusterNode
{
    int id;                  // 节点ID
    int port;                // 服务端口
    pid_t pid;               // 进程ID
    bool is_alive;           // 是否存活
    bool is_leader;          // 是否为领导者
    std::string ip;          // IP地址
    uint64_t last_heartbeat; // 最后心跳时间

    ClusterNode(int i, int p, const std::string &addr = "127.0.0.1")
        : id(i), port(p), pid(-1), is_alive(false), is_leader(false),
          ip(addr), last_heartbeat(0) {}
};

std::vector<ClusterNode> g_cluster_nodes;
std::mutex g_cluster_mutex;

// ==================== 工具函数 ====================

/**
 * @brief 获取当前时间戳（微秒）
 */
uint64_t getCurrentTimestampUs()
{
    return std::chrono::duration_cast<std::chrono::microseconds>(
               std::chrono::high_resolution_clock::now().time_since_epoch())
        .count();
}

/**
 * @brief 格式化字节数
 */
std::string formatBytes(uint64_t bytes)
{
    const char *units[] = {"B", "KB", "MB", "GB"};
    int unit = 0;
    double size = bytes;

    while (size >= 1024 && unit < 3)
    {
        size /= 1024;
        unit++;
    }

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return oss.str();
}

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig)
{
    std::cout << "\n[🛑 集成测试] 收到信号 " << sig << "，开始优雅关闭..." << std::endl;
    g_test_running = false;

    // 停止所有节点
    std::lock_guard<std::mutex> lock(g_cluster_mutex);
    for (auto &node : g_cluster_nodes)
    {
        if (node.pid > 0)
        {
            std::cout << "[🛑 集群管理] 停止节点 " << node.id << " (PID: " << node.pid << ")" << std::endl;
            kill(node.pid, SIGTERM);
            int status;
            waitpid(node.pid, &status, 0);
            node.is_alive = false;
        }
    }

    // 打印最终统计
    std::cout << "\n========== 🏁 最终统计报告 ==========" << std::endl;
    std::cout << "📊 总请求数: " << g_stats.total_requests.load() << std::endl;
    std::cout << "✅ 成功请求: " << g_stats.successful_requests.load() << std::endl;
    std::cout << "❌ 失败请求: " << g_stats.failed_requests.load() << std::endl;
    std::cout << "⏱️  平均延迟: " << std::fixed << std::setprecision(2)
              << g_stats.getAverageLatency() << " μs" << std::endl;
    std::cout << "📈 成功率: " << std::fixed << std::setprecision(2)
              << g_stats.getSuccessRate() << "%" << std::endl;
    std::cout << "======================================" << std::endl;

    exit(0);
}

/**
 * @brief 生成随机端口
 */
int generate_port()
{
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(25000, 35000);
    return dis(gen);
}

/**
 * @brief 创建集群配置文件
 */
bool create_cluster_config(const std::string &filename)
{
    std::ofstream ofs(filename, std::ios::trunc);
    if (!ofs.is_open())
    {
        return false;
    }

    std::lock_guard<std::mutex> lock(g_cluster_mutex);
    for (const auto &node : g_cluster_nodes)
    {
        ofs << node.ip << ":" << node.port << std::endl;
    }

    ofs.close();
    return true;
}

// ==================== 基于Fiber协程的KV客户端 ====================

/**
 * @brief 高性能KV客户端，基于fiber协程和异步RPC
 *
 * 该客户端展示了三个模块的完美协同：
 * - Fiber协程：高并发、低延迟的协程调度
 * - RPC通信：高效的protobuf序列化和网络传输
 * - Raft一致性：强一致性的分布式存储
 */
class FiberKvClient
{
public:
    /**
     * @brief 构造函数
     * @param cluster_nodes 集群节点列表
     * @param client_id 客户端唯一标识
     */
    FiberKvClient(const std::vector<ClusterNode> &cluster_nodes, const std::string &client_id)
        : cluster_nodes_(cluster_nodes), client_id_(client_id), request_id_(0)
    {

        // 初始化随机数生成器
        rng_.seed(std::chrono::steady_clock::now().time_since_epoch().count());

        std::cout << "[🚀 KV客户端] 初始化完成，客户端ID: " << client_id_
                  << "，集群节点数: " << cluster_nodes_.size() << std::endl;
    }

    /**
     * @brief 异步Get操作
     */
    void asyncGet(const std::string &key, std::function<void(bool, const std::string &)> callback)
    {
        auto start_time = getCurrentTimestampUs();

        // 在协程中执行
        auto fiber = std::make_shared<monsoon::Fiber>([this, key, callback, start_time]()
                                                      {
            g_stats.fiber_count++;
            g_stats.get_operations++;

            bool success = false;
            std::string value;

            // 尝试所有节点，直到找到leader或全部失败
            for (int attempt = 0; attempt < 3 && g_test_running; attempt++) {
                auto node = selectRandomNode();
                if (!node.is_alive) continue;

                try {
                    MprpcChannel channel(node.ip, node.port, true);
                    raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);

                    raftKVRpcProctoc::GetArgs request;
                    request.set_key(key);
                    request.set_clientid(client_id_);
                    request.set_requestid(++request_id_);

                    raftKVRpcProctoc::GetReply response;
                    MprpcController controller;

                    g_stats.rpc_calls++;
                    stub.Get(&controller, &request, &response, nullptr);

                    if (!controller.Failed()) {
                        if (response.err() == "OK") {
                            success = true;
                            value = response.value();
                            g_stats.successful_requests++;
                            break;
                        } else if (response.err() == "ErrWrongLeader") {
                            // 不是leader，尝试下一个节点
                            continue;
                        } else if (response.err() == "ErrNoKey") {
                            // 键不存在，也算成功
                            success = true;
                            value = "";
                            g_stats.successful_requests++;
                            break;
                        }
                    }
                } catch (const std::exception& e) {
                    std::cout << "[❌ KV客户端] Get操作异常: " << e.what() << std::endl;
                }

                // 短暂等待后重试
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }

            if (!success) {
                g_stats.failed_requests++;
            }

            // 更新延迟统计
            auto end_time = getCurrentTimestampUs();
            g_stats.updateLatency(end_time - start_time);
            g_stats.total_requests++;

            // 执行回调
            callback(success, value); });

        fiber->resume();
    }

    /**
     * @brief 异步Put操作
     */
    void asyncPut(const std::string &key, const std::string &value,
                  std::function<void(bool)> callback)
    {
        auto start_time = getCurrentTimestampUs();

        auto fiber = std::make_shared<monsoon::Fiber>([this, key, value, callback, start_time]()
                                                      {
            g_stats.fiber_count++;
            g_stats.put_operations++;

            bool success = false;

            for (int attempt = 0; attempt < 3 && g_test_running; attempt++) {
                auto node = selectRandomNode();
                if (!node.is_alive) continue;

                try {
                    MprpcChannel channel(node.ip, node.port, true);
                    raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);

                    raftKVRpcProctoc::PutAppendArgs request;
                    request.set_key(key);
                    request.set_value(value);
                    request.set_op("Put");
                    request.set_clientid(client_id_);
                    request.set_requestid(++request_id_);

                    raftKVRpcProctoc::PutAppendReply response;
                    MprpcController controller;

                    g_stats.rpc_calls++;
                    stub.PutAppend(&controller, &request, &response, nullptr);

                    if (!controller.Failed() && response.err() == "OK") {
                        success = true;
                        g_stats.successful_requests++;
                        break;
                    } else if (response.err() == "ErrWrongLeader") {
                        continue;
                    }
                } catch (const std::exception& e) {
                    std::cout << "[❌ KV客户端] Put操作异常: " << e.what() << std::endl;
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }

            if (!success) {
                g_stats.failed_requests++;
            }

            auto end_time = getCurrentTimestampUs();
            g_stats.updateLatency(end_time - start_time);
            g_stats.total_requests++;

            callback(success); });

        fiber->resume();
    }

    /**
     * @brief 异步Append操作
     */
    void asyncAppend(const std::string &key, const std::string &value,
                     std::function<void(bool)> callback)
    {
        auto start_time = getCurrentTimestampUs();

        auto fiber = std::make_shared<monsoon::Fiber>([this, key, value, callback, start_time]()
                                                      {
            g_stats.fiber_count++;
            g_stats.append_operations++;

            bool success = false;

            for (int attempt = 0; attempt < 3 && g_test_running; attempt++) {
                auto node = selectRandomNode();
                if (!node.is_alive) continue;

                try {
                    MprpcChannel channel(node.ip, node.port, true);
                    raftKVRpcProctoc::kvServerRpc_Stub stub(&channel);

                    raftKVRpcProctoc::PutAppendArgs request;
                    request.set_key(key);
                    request.set_value(value);
                    request.set_op("Append");
                    request.set_clientid(client_id_);
                    request.set_requestid(++request_id_);

                    raftKVRpcProctoc::PutAppendReply response;
                    MprpcController controller;

                    g_stats.rpc_calls++;
                    stub.PutAppend(&controller, &request, &response, nullptr);

                    if (!controller.Failed() && response.err() == "OK") {
                        success = true;
                        g_stats.successful_requests++;
                        break;
                    } else if (response.err() == "ErrWrongLeader") {
                        continue;
                    }
                } catch (const std::exception& e) {
                    std::cout << "[❌ KV客户端] Append操作异常: " << e.what() << std::endl;
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }

            if (!success) {
                g_stats.failed_requests++;
            }

            auto end_time = getCurrentTimestampUs();
            g_stats.updateLatency(end_time - start_time);
            g_stats.total_requests++;

            callback(success); });

        fiber->resume();
    }

private:
    /**
     * @brief 随机选择一个存活的节点
     */
    ClusterNode selectRandomNode()
    {
        std::lock_guard<std::mutex> lock(g_cluster_mutex);

        std::vector<ClusterNode> alive_nodes;
        for (const auto &node : cluster_nodes_)
        {
            if (node.is_alive)
            {
                alive_nodes.push_back(node);
            }
        }

        if (alive_nodes.empty())
        {
            return ClusterNode(-1, -1); // 返回无效节点
        }

        std::uniform_int_distribution<> dis(0, alive_nodes.size() - 1);
        return alive_nodes[dis(rng_)];
    }

    std::vector<ClusterNode> cluster_nodes_;
    std::string client_id_;
    std::atomic<int> request_id_;
    std::mt19937 rng_;
};

// ==================== 集群管理 ====================

/**
 * @brief 启动Raft集群
 */
bool start_cluster(int node_count)
{
    std::cout << "\n🚀 ========== 启动Raft-KV集群 ==========" << std::endl;
    std::cout << "📊 节点数量: " << node_count << std::endl;

    // 初始化节点信息
    {
        std::lock_guard<std::mutex> lock(g_cluster_mutex);
        g_cluster_nodes.clear();
        int base_port = generate_port();

        for (int i = 0; i < node_count; i++)
        {
            g_cluster_nodes.emplace_back(i, base_port + i);
            std::cout << "🔧 节点" << i << " 配置: " << g_cluster_nodes[i].ip
                      << ":" << g_cluster_nodes[i].port << std::endl;
        }
    }

    // 创建配置文件
    std::string config_file = "integration_test_config.txt";
    if (!create_cluster_config(config_file))
    {
        std::cerr << "❌ [集群管理] 创建配置文件失败" << std::endl;
        return false;
    }
    std::cout << "📝 集群配置文件已创建: " << config_file << std::endl;

    // 启动节点
    for (auto &node : g_cluster_nodes)
    {
        std::cout << "🔄 启动节点" << node.id << " (端口: " << node.port << ")..." << std::endl;

        pid_t pid = fork();
        if (pid == 0)
        {
            // 子进程：启动KV服务器
            try
            {
                KvServer kv(node.id, -1, config_file, node.port);
                // 启动后进入无限循环，保持服务运行
                while (true)
                {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "❌ [节点" << node.id << "] 启动失败: " << e.what() << std::endl;
                exit(1);
            }
            exit(0);
        }
        else if (pid > 0)
        {
            std::lock_guard<std::mutex> lock(g_cluster_mutex);
            node.pid = pid;
            node.is_alive = true;
            node.last_heartbeat = getCurrentTimestampUs();
            std::cout << "✅ [集群管理] 节点" << node.id << " 已启动 (PID: " << pid << ")" << std::endl;
        }
        else
        {
            std::cerr << "❌ [集群管理] 启动节点" << node.id << " 失败" << std::endl;
            return false;
        }

        // 节点间启动间隔，避免端口冲突
        std::this_thread::sleep_for(std::chrono::milliseconds(800));
    }

    std::cout << "⏳ 等待集群稳定和领导者选举..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(5));

    std::cout << "🎉 Raft-KV集群启动完成！" << std::endl;
    return true;
}

/**
 * @brief 停止集群
 */
void stop_cluster()
{
    std::cout << "\n🛑 ========== 停止Raft-KV集群 ==========" << std::endl;

    std::lock_guard<std::mutex> lock(g_cluster_mutex);
    for (auto &node : g_cluster_nodes)
    {
        if (node.pid > 0)
        {
            std::cout << "🔄 停止节点" << node.id << " (PID: " << node.pid << ")..." << std::endl;
            kill(node.pid, SIGTERM);
            int status;
            waitpid(node.pid, &status, 0);
            node.is_alive = false;
            std::cout << "✅ 节点" << node.id << " 已停止" << std::endl;
        }
    }

    // 清理配置文件
    std::remove("integration_test_config.txt");
    std::cout << "🧹 配置文件已清理" << std::endl;
    std::cout << "🏁 集群停止完成" << std::endl;
}

/**
 * @brief 打印实时统计信息
 */
void print_stats()
{
    std::cout << "\n📊 ========== 实时统计 ==========" << std::endl;
    std::cout << "🚀 协程数量: " << g_stats.fiber_count.load() << std::endl;
    std::cout << "📞 RPC调用: " << g_stats.rpc_calls.load() << std::endl;
    std::cout << "📊 总请求: " << g_stats.total_requests.load() << std::endl;
    std::cout << "✅ 成功: " << g_stats.successful_requests.load() << std::endl;
    std::cout << "❌ 失败: " << g_stats.failed_requests.load() << std::endl;
    std::cout << "⏱️  超时: " << g_stats.timeout_requests.load() << std::endl;

    if (g_stats.total_requests.load() > 0)
    {
        std::cout << "📈 成功率: " << std::fixed << std::setprecision(2)
                  << g_stats.getSuccessRate() << "%" << std::endl;
        std::cout << "⚡ 平均延迟: " << std::fixed << std::setprecision(2)
                  << g_stats.getAverageLatency() << " μs" << std::endl;
        std::cout << "🔥 最小延迟: " << g_stats.min_latency_us.load() << " μs" << std::endl;
        std::cout << "🔥 最大延迟: " << g_stats.max_latency_us.load() << " μs" << std::endl;
    }

    std::cout << "📤 发送: " << formatBytes(g_stats.bytes_sent.load()) << std::endl;
    std::cout << "📥 接收: " << formatBytes(g_stats.bytes_received.load()) << std::endl;
    std::cout << "🔄 领导者变更: " << g_stats.leader_changes.load() << std::endl;
    std::cout << "=================================" << std::endl;
}

// ==================== 测试场景 ====================

/**
 * @brief 基本功能集成测试
 * 测试Get、Put、Append的基本功能
 */
void test_basic_integration()
{
    std::cout << "\n🧪 ========== 基本功能测试 ==========" << std::endl;

    if (g_cluster_nodes.empty())
    {
        std::cout << "❌ [基本测试] 没有可用节点" << std::endl;
        return;
    }

    // 创建KV客户端
    FiberKvClient client(g_cluster_nodes, "basic_test_client");

    std::cout << "🔍 测试基本KV操作..." << std::endl;

    // 测试Put操作
    std::atomic<int> completed_ops{0};

    client.asyncPut("test_key_1", "test_value_1", [&](bool success)
                    {
        if (success) {
            std::cout << "✅ Put操作成功: test_key_1 = test_value_1" << std::endl;
        } else {
            std::cout << "❌ Put操作失败: test_key_1" << std::endl;
        }
        completed_ops++; });

    // 测试Get操作
    client.asyncGet("test_key_1", [&](bool success, const std::string &value)
                    {
        if (success) {
            std::cout << "✅ Get操作成功: test_key_1 = " << value << std::endl;
        } else {
            std::cout << "❌ Get操作失败: test_key_1" << std::endl;
        }
        completed_ops++; });

    // 测试Append操作
    client.asyncAppend("test_key_1", "_appended", [&](bool success)
                       {
        if (success) {
            std::cout << "✅ Append操作成功: test_key_1 += _appended" << std::endl;
        } else {
            std::cout << "❌ Append操作失败: test_key_1" << std::endl;
        }
        completed_ops++; });

    // 验证Append结果
    client.asyncGet("test_key_1", [&](bool success, const std::string &value)
                    {
        if (success) {
            std::cout << "✅ Append验证成功: test_key_1 = " << value << std::endl;
        } else {
            std::cout << "❌ Append验证失败: test_key_1" << std::endl;
        }
        completed_ops++; });

    // 等待所有操作完成
    while (completed_ops.load() < 4 && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    print_stats();
    std::cout << "🎉 基本功能测试完成" << std::endl;
}

/**
 * @brief 高并发协程测试
 * 测试数百个协程同时进行KV操作
 */
void test_concurrent_fibers()
{
    std::cout << "\n🚀 ========== 高并发协程测试 ==========" << std::endl;

    if (g_cluster_nodes.empty())
    {
        std::cout << "❌ [并发测试] 没有可用节点" << std::endl;
        return;
    }

    const int CONCURRENT_CLIENTS = 50; // 并发客户端数量
    const int OPS_PER_CLIENT = 10;     // 每个客户端的操作数

    std::cout << "🔥 启动 " << CONCURRENT_CLIENTS << " 个并发客户端，每个执行 "
              << OPS_PER_CLIENT << " 个操作" << std::endl;

    std::atomic<int> completed_clients{0};
    std::vector<std::unique_ptr<FiberKvClient>> clients;

    auto start_time = getCurrentTimestampUs();

    // 创建并发客户端
    for (int i = 0; i < CONCURRENT_CLIENTS; i++)
    {
        auto client = std::make_unique<FiberKvClient>(
            g_cluster_nodes, "concurrent_client_" + std::to_string(i));

        // 每个客户端在协程中执行操作
        auto fiber = std::make_shared<monsoon::Fiber>([&client, i, OPS_PER_CLIENT, &completed_clients]()
                                                      {
            for (int j = 0; j < OPS_PER_CLIENT && g_test_running; j++) {
                std::string key = "concurrent_key_" + std::to_string(i) + "_" + std::to_string(j);
                std::string value = "value_" + std::to_string(i) + "_" + std::to_string(j);

                // 随机选择操作类型
                int op_type = j % 3;
                if (op_type == 0) {
                    // Put操作
                    client->asyncPut(key, value, [](bool success) {
                        // 静默处理结果
                    });
                } else if (op_type == 1) {
                    // Get操作
                    client->asyncGet(key, [](bool success, const std::string& val) {
                        // 静默处理结果
                    });
                } else {
                    // Append操作
                    client->asyncAppend(key, "_append", [](bool success) {
                        // 静默处理结果
                    });
                }

                // 短暂间隔
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            completed_clients++; });

        fiber->resume();
        clients.push_back(std::move(client));
    }

    // 等待所有客户端完成
    while (completed_clients.load() < CONCURRENT_CLIENTS && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 每秒打印一次进度
        static auto last_print = getCurrentTimestampUs();
        auto now = getCurrentTimestampUs();
        if (now - last_print > 1000000)
        { // 1秒
            std::cout << "📊 进度: " << completed_clients.load() << "/"
                      << CONCURRENT_CLIENTS << " 客户端完成" << std::endl;
            last_print = now;
        }
    }

    auto end_time = getCurrentTimestampUs();
    auto duration_ms = (end_time - start_time) / 1000;

    std::cout << "⏱️  并发测试耗时: " << duration_ms << " ms" << std::endl;
    print_stats();
    std::cout << "🎉 高并发协程测试完成" << std::endl;
}

/**
 * @brief 故障恢复测试
 * 测试节点故障时的自动恢复能力
 */
void test_fault_tolerance()
{
    std::cout << "\n💥 ========== 故障恢复测试 ==========" << std::endl;

    if (g_cluster_nodes.size() < 3)
    {
        std::cout << "⚠️  [故障测试] 节点数量不足（需要至少3个节点），跳过故障测试" << std::endl;
        return;
    }

    FiberKvClient client(g_cluster_nodes, "fault_test_client");

    // 先进行正常操作，建立基线数据
    std::cout << "📝 建立基线数据..." << std::endl;
    std::atomic<int> baseline_ops{0};

    for (int i = 0; i < 5; i++)
    {
        std::string key = "fault_test_key_" + std::to_string(i);
        std::string value = "baseline_value_" + std::to_string(i);

        client.asyncPut(key, value, [&](bool success)
                        {
            if (success) {
                std::cout << "✅ 基线数据写入成功: " << key << std::endl;
            }
            baseline_ops++; });
    }

    // 等待基线操作完成
    while (baseline_ops.load() < 5 && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    std::cout << "💀 模拟节点0故障..." << std::endl;
    {
        std::lock_guard<std::mutex> lock(g_cluster_mutex);
        if (g_cluster_nodes[0].pid > 0)
        {
            kill(g_cluster_nodes[0].pid, SIGKILL);
            g_cluster_nodes[0].is_alive = false;
            std::cout << "🔥 节点0已被强制终止" << std::endl;
        }
    }

    // 等待故障检测和领导者重新选举
    std::cout << "⏳ 等待故障检测和领导者重新选举..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // 尝试在故障后继续操作
    std::cout << "🔄 故障后继续操作..." << std::endl;
    std::atomic<int> recovery_ops{0};

    for (int i = 0; i < 5; i++)
    {
        std::string key = "fault_test_key_" + std::to_string(i);

        // 尝试读取之前写入的数据
        client.asyncGet(key, [&, i](bool success, const std::string &value)
                        {
            if (success) {
                std::cout << "✅ 故障后读取成功: " << key << " = " << value << std::endl;
            } else {
                std::cout << "❌ 故障后读取失败: " << key << std::endl;
            }
            recovery_ops++; });
    }

    // 等待恢复操作完成
    while (recovery_ops.load() < 5 && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    print_stats();
    std::cout << "🎉 故障恢复测试完成" << std::endl;
}

/**
 * @brief 性能基准测试
 * 测试系统的极限性能和吞吐量
 */
void test_performance_benchmark()
{
    std::cout << "\n⚡ ========== 性能基准测试 ==========" << std::endl;

    if (g_cluster_nodes.empty())
    {
        std::cout << "❌ [性能测试] 没有可用节点" << std::endl;
        return;
    }

    const int PERFORMANCE_CLIENTS = 100; // 性能测试客户端数量
    const int OPS_PER_CLIENT = 20;       // 每个客户端的操作数

    std::cout << "🚀 启动 " << PERFORMANCE_CLIENTS << " 个性能测试客户端" << std::endl;
    std::cout << "📊 每个客户端执行 " << OPS_PER_CLIENT << " 个操作" << std::endl;
    std::cout << "🎯 总操作数: " << (PERFORMANCE_CLIENTS * OPS_PER_CLIENT) << std::endl;

    // 重置统计
    g_stats.reset();

    auto start_time = getCurrentTimestampUs();
    std::atomic<int> completed_perf_clients{0};
    std::vector<std::unique_ptr<FiberKvClient>> perf_clients;

    // 创建性能测试客户端
    for (int i = 0; i < PERFORMANCE_CLIENTS; i++)
    {
        auto client = std::make_unique<FiberKvClient>(
            g_cluster_nodes, "perf_client_" + std::to_string(i));

        // 每个客户端在协程中执行高强度操作
        auto fiber = std::make_shared<monsoon::Fiber>([&client, i, OPS_PER_CLIENT, &completed_perf_clients]()
                                                      {
            for (int j = 0; j < OPS_PER_CLIENT && g_test_running; j++) {
                std::string key = "perf_key_" + std::to_string(i) + "_" + std::to_string(j);
                std::string value = "perf_value_" + std::to_string(i) + "_" + std::to_string(j);

                // 混合操作模式
                if (j % 4 == 0) {
                    // 25% Put操作
                    client->asyncPut(key, value, [](bool success) {});
                } else if (j % 4 == 1) {
                    // 25% Get操作
                    client->asyncGet(key, [](bool success, const std::string& val) {});
                } else if (j % 4 == 2) {
                    // 25% Append操作
                    client->asyncAppend(key, "_perf", [](bool success) {});
                } else {
                    // 25% 随机读取
                    std::string random_key = "perf_key_" + std::to_string(rand() % PERFORMANCE_CLIENTS)
                                           + "_" + std::to_string(rand() % OPS_PER_CLIENT);
                    client->asyncGet(random_key, [](bool success, const std::string& val) {});
                }

                // 无间隔，最大压力测试
            }
            completed_perf_clients++; });

        fiber->resume();
        perf_clients.push_back(std::move(client));
    }

    // 监控进度
    std::cout << "🔥 性能测试进行中..." << std::endl;
    while (completed_perf_clients.load() < PERFORMANCE_CLIENTS && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(500));

        // 每2秒打印一次进度
        static auto last_print = getCurrentTimestampUs();
        auto now = getCurrentTimestampUs();
        if (now - last_print > 2000000)
        { // 2秒
            auto current_qps = g_stats.getQPS((now - start_time) / 1000);
            std::cout << "📊 进度: " << completed_perf_clients.load() << "/"
                      << PERFORMANCE_CLIENTS << " 客户端完成，当前QPS: "
                      << std::fixed << std::setprecision(1) << current_qps << std::endl;
            last_print = now;
        }
    }

    auto end_time = getCurrentTimestampUs();
    auto duration_ms = (end_time - start_time) / 1000;

    std::cout << "\n🏆 ========== 性能测试结果 ==========" << std::endl;
    std::cout << "⏱️  总耗时: " << duration_ms << " ms" << std::endl;
    std::cout << "🚀 总协程数: " << g_stats.fiber_count.load() << std::endl;
    std::cout << "📞 总RPC调用: " << g_stats.rpc_calls.load() << std::endl;
    std::cout << "📊 总操作数: " << g_stats.total_requests.load() << std::endl;
    std::cout << "✅ 成功操作: " << g_stats.successful_requests.load() << std::endl;
    std::cout << "❌ 失败操作: " << g_stats.failed_requests.load() << std::endl;
    std::cout << "📈 成功率: " << std::fixed << std::setprecision(2)
              << g_stats.getSuccessRate() << "%" << std::endl;
    std::cout << "⚡ 平均QPS: " << std::fixed << std::setprecision(1)
              << g_stats.getQPS(duration_ms) << std::endl;
    std::cout << "⚡ 平均延迟: " << std::fixed << std::setprecision(2)
              << g_stats.getAverageLatency() << " μs" << std::endl;
    std::cout << "🔥 最小延迟: " << g_stats.min_latency_us.load() << " μs" << std::endl;
    std::cout << "🔥 最大延迟: " << g_stats.max_latency_us.load() << " μs" << std::endl;
    std::cout << "=================================" << std::endl;

    std::cout << "🎉 性能基准测试完成" << std::endl;
}

/**
 * @brief 长时间稳定性测试
 * 测试系统在长时间运行下的稳定性
 */
void test_long_running_stability(int duration_seconds)
{
    std::cout << "\n🕐 ========== 长时间稳定性测试 ==========" << std::endl;
    std::cout << "⏰ 测试时长: " << duration_seconds << " 秒" << std::endl;

    if (g_cluster_nodes.empty())
    {
        std::cout << "❌ [稳定性测试] 没有可用节点" << std::endl;
        return;
    }

    auto start_time = std::chrono::steady_clock::now();
    auto end_time = start_time + std::chrono::seconds(duration_seconds);

    // 创建多个长期运行的客户端
    const int STABILITY_CLIENTS = 10;
    std::vector<std::unique_ptr<FiberKvClient>> stability_clients;
    std::atomic<int> stability_rounds{0};

    std::cout << "🔄 启动 " << STABILITY_CLIENTS << " 个稳定性测试客户端" << std::endl;

    for (int i = 0; i < STABILITY_CLIENTS; i++)
    {
        auto client = std::make_unique<FiberKvClient>(
            g_cluster_nodes, "stability_client_" + std::to_string(i));

        // 每个客户端持续运行
        auto fiber = std::make_shared<monsoon::Fiber>([&client, i, end_time, &stability_rounds]()
                                                      {
            int round = 0;
            while (std::chrono::steady_clock::now() < end_time && g_test_running) {
                round++;
                std::string key = "stability_key_" + std::to_string(i) + "_" + std::to_string(round);
                std::string value = "stability_value_" + std::to_string(round);

                // 执行混合操作
                if (round % 3 == 0) {
                    client->asyncPut(key, value, [](bool success) {});
                } else if (round % 3 == 1) {
                    client->asyncGet(key, [](bool success, const std::string& val) {});
                } else {
                    client->asyncAppend(key, "_stable", [](bool success) {});
                }

                // 每轮操作间隔
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                stability_rounds++;
            } });

        fiber->resume();
        stability_clients.push_back(std::move(client));
    }

    // 监控稳定性测试进度
    std::cout << "🔄 稳定性测试进行中..." << std::endl;
    auto last_stats_time = std::chrono::steady_clock::now();

    while (std::chrono::steady_clock::now() < end_time && g_test_running)
    {
        std::this_thread::sleep_for(std::chrono::seconds(5));

        auto now = std::chrono::steady_clock::now();
        auto remaining = std::chrono::duration_cast<std::chrono::seconds>(end_time - now).count();

        // 每30秒打印一次详细统计
        if (std::chrono::duration_cast<std::chrono::seconds>(now - last_stats_time).count() >= 30)
        {
            std::cout << "\n📊 稳定性测试中期报告 (剩余 " << remaining << " 秒)" << std::endl;
            std::cout << "🔄 已完成轮次: " << stability_rounds.load() << std::endl;
            print_stats();
            last_stats_time = now;
        }
        else
        {
            std::cout << "⏳ 稳定性测试运行中... 剩余时间: " << remaining << " 秒" << std::endl;
        }
    }

    std::cout << "\n🏁 稳定性测试完成" << std::endl;
    std::cout << "🔄 总轮次: " << stability_rounds.load() << std::endl;
    print_stats();
    std::cout << "🎉 长时间稳定性测试完成" << std::endl;
}

// ==================== 主函数 ====================

int main(int argc, char **argv)
{
    std::cout << "🌟 ========================================== 🌟" << std::endl;
    std::cout << "🚀    Fiber + RPC + Raft 完美协同集成测试    🚀" << std::endl;
    std::cout << "🌟 ========================================== 🌟" << std::endl;
    std::cout << "💡 展示三大核心模块的完美协同：" << std::endl;
    std::cout << "   🧵 Fiber协程：高并发、低延迟调度" << std::endl;
    std::cout << "   🌐 RPC通信：高效protobuf序列化" << std::endl;
    std::cout << "   🔄 Raft共识：分布式强一致性" << std::endl;
    std::cout << "🌟 ========================================== 🌟" << std::endl;

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 默认参数
    int node_count = 3;
    int test_duration = 60; // 默认60秒稳定性测试
    bool enable_performance = false;
    bool enable_fault_test = false;

    // 解析命令行参数
    if (argc >= 2)
    {
        node_count = std::atoi(argv[1]);
        if (node_count < 1 || node_count > 10)
        {
            std::cerr << "❌ 节点数量必须在1-10之间" << std::endl;
            return 1;
        }
    }
    if (argc >= 3)
    {
        test_duration = std::atoi(argv[2]);
        if (test_duration < 0)
            test_duration = 0;
    }
    if (argc >= 4)
    {
        enable_performance = (std::string(argv[3]) == "perf");
    }
    if (argc >= 5)
    {
        enable_fault_test = (std::string(argv[4]) == "fault");
    }

    std::cout << "⚙️  配置参数:" << std::endl;
    std::cout << "   📊 集群节点数: " << node_count << std::endl;
    std::cout << "   ⏰ 稳定性测试时长: " << test_duration << " 秒" << std::endl;
    std::cout << "   ⚡ 性能测试: " << (enable_performance ? "启用" : "禁用") << std::endl;
    std::cout << "   💥 故障测试: " << (enable_fault_test ? "启用" : "禁用") << std::endl;
    std::cout << "💡 使用方法: " << argv[0] << " [节点数] [测试时长] [perf] [fault]" << std::endl;
    std::cout << "🌟 ========================================== 🌟" << std::endl;

    try
    {
        // 启动集群
        if (!start_cluster(node_count))
        {
            std::cerr << "❌ 启动集群失败" << std::endl;
            return 1;
        }

        // 执行测试套件
        std::cout << "\n🧪 开始执行测试套件..." << std::endl;

        // 1. 基本功能测试（必须）
        test_basic_integration();

        // 2. 并发协程测试（必须）
        test_concurrent_fibers();

        // 3. 故障恢复测试（可选）
        if (enable_fault_test && node_count >= 3)
        {
            test_fault_tolerance();
        }
        else if (enable_fault_test)
        {
            std::cout << "⚠️  故障测试需要至少3个节点，跳过" << std::endl;
        }

        // 4. 性能基准测试（可选）
        if (enable_performance)
        {
            test_performance_benchmark();
        }

        // 5. 长时间稳定性测试（可选）
        if (test_duration > 0)
        {
            test_long_running_stability(test_duration);
        }

        // 停止集群
        stop_cluster();

        // 显示最终统计报告
        std::cout << "\n🏆 ========== 最终测试报告 ==========" << std::endl;
        std::cout << "🎉 集成测试完成！" << std::endl;
        std::cout << "📊 最终统计:" << std::endl;
        std::cout << "   🚀 协程总数: " << g_stats.fiber_count.load() << std::endl;
        std::cout << "   📞 RPC调用: " << g_stats.rpc_calls.load() << std::endl;
        std::cout << "   📊 总请求数: " << g_stats.total_requests.load() << std::endl;
        std::cout << "   ✅ 成功请求: " << g_stats.successful_requests.load() << std::endl;
        std::cout << "   ❌ 失败请求: " << g_stats.failed_requests.load() << std::endl;
        std::cout << "   ⏱️  超时请求: " << g_stats.timeout_requests.load() << std::endl;

        if (g_stats.total_requests.load() > 0)
        {
            std::cout << "   📈 总体成功率: " << std::fixed << std::setprecision(2)
                      << g_stats.getSuccessRate() << "%" << std::endl;
            std::cout << "   ⚡ 平均延迟: " << std::fixed << std::setprecision(2)
                      << g_stats.getAverageLatency() << " μs" << std::endl;
        }

        std::cout << "   📤 发送数据: " << formatBytes(g_stats.bytes_sent.load()) << std::endl;
        std::cout << "   📥 接收数据: " << formatBytes(g_stats.bytes_received.load()) << std::endl;
        std::cout << "   🔄 领导者变更: " << g_stats.leader_changes.load() << std::endl;

        std::cout << "\n🌟 ========================================== 🌟" << std::endl;
        std::cout << "🎊 恭喜！Fiber+RPC+Raft三模块协同测试圆满完成！" << std::endl;
        std::cout << "💪 系统展现了出色的并发性能和一致性保证！" << std::endl;
        std::cout << "🌟 ========================================== 🌟" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "❌ 集成测试过程中发生异常: " << e.what() << std::endl;
        stop_cluster();
        return 1;
    }

    return 0;
}

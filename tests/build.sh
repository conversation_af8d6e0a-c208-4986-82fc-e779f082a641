#!/bin/bash

# 🌟 Raft-KV 集成测试自动构建脚本
# 自动化编译和测试流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🌟 ========================================== 🌟${NC}"
    echo -e "${CYAN}🚀    $1    🚀${NC}"
    echo -e "${PURPLE}🌟 ========================================== 🌟${NC}"
}

# 检查依赖
check_dependencies() {
    print_header "检查编译依赖"
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        print_error "未找到CMake，请先安装CMake"
        exit 1
    fi
    print_success "CMake: $(cmake --version | head -n1)"
    
    # 检查编译器
    if ! command -v g++ &> /dev/null; then
        print_error "未找到G++编译器，请先安装"
        exit 1
    fi
    print_success "G++: $(g++ --version | head -n1)"
    
    # 检查protobuf
    if ! command -v protoc &> /dev/null; then
        print_warning "未找到protoc编译器，可能影响protobuf文件生成"
    else
        print_success "Protobuf: $(protoc --version)"
    fi
    
    # 检查pkg-config
    if command -v pkg-config &> /dev/null; then
        if pkg-config --exists protobuf; then
            print_success "Protobuf库: $(pkg-config --modversion protobuf)"
        else
            print_warning "未找到protobuf开发库"
        fi
    fi
}

# 生成protobuf文件
generate_protobuf() {
    print_header "生成Protobuf文件"
    
    PROTO_DIR="../raftRpcPro"
    PROTO_FILE="$PROTO_DIR/kvServerRPC.proto"
    
    if [ -f "$PROTO_FILE" ]; then
        print_info "找到protobuf定义文件: $PROTO_FILE"
        
        if command -v protoc &> /dev/null; then
            print_info "生成C++代码..."
            protoc --cpp_out="$PROTO_DIR" --proto_path="$PROTO_DIR" "$PROTO_FILE"
            
            if [ -f "$PROTO_DIR/kvServerRPC.pb.cc" ]; then
                print_success "Protobuf C++文件生成成功"
            else
                print_warning "Protobuf文件生成可能失败"
            fi
        else
            print_warning "未找到protoc，跳过protobuf文件生成"
        fi
    else
        print_warning "未找到protobuf定义文件: $PROTO_FILE"
    fi
}

# 清理构建目录
clean_build() {
    print_header "清理构建目录"
    
    if [ -d "build" ]; then
        print_info "删除现有构建目录..."
        rm -rf build
        print_success "构建目录已清理"
    fi
    
    # 清理测试配置文件
    if [ -f "integration_test_config.txt" ]; then
        rm -f integration_test_config.txt
        print_success "测试配置文件已清理"
    fi
}

# 配置和编译
build_project() {
    print_header "配置和编译项目"
    
    # 创建构建目录
    mkdir -p build
    cd build
    
    # 确定构建类型
    BUILD_TYPE=${1:-Release}
    print_info "构建类型: $BUILD_TYPE"
    
    # 配置CMake
    print_info "配置CMake..."
    cmake .. -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
    
    # 编译
    print_info "开始编译..."
    NPROC=$(nproc 2>/dev/null || echo 4)
    make -j"$NPROC"
    
    cd ..
    
    if [ -f "build/bin/integration_test" ] && [ -f "build/bin/kv_client_example" ]; then
        print_success "编译完成！可执行文件位于 build/bin/ 目录"
    else
        print_error "编译失败，请检查错误信息"
        exit 1
    fi
}

# 运行测试
run_tests() {
    print_header "运行测试"
    
    if [ ! -f "build/bin/integration_test" ]; then
        print_error "未找到集成测试可执行文件，请先编译"
        exit 1
    fi
    
    print_info "运行基本功能测试..."
    cd build
    make run_test || print_warning "基本测试可能失败"
    cd ..
}

# 显示帮助信息
show_help() {
    print_header "构建脚本帮助"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  help          显示此帮助信息"
    echo "  check         检查编译依赖"
    echo "  clean         清理构建目录"
    echo "  debug         编译Debug版本"
    echo "  release       编译Release版本（默认）"
    echo "  test          编译并运行基本测试"
    echo "  all           完整流程：检查依赖 -> 清理 -> 编译 -> 测试"
    echo ""
    echo "示例:"
    echo "  $0 all        # 完整构建流程"
    echo "  $0 release    # 编译Release版本"
    echo "  $0 debug      # 编译Debug版本"
    echo "  $0 test       # 编译并测试"
    echo ""
}

# 主函数
main() {
    case "${1:-all}" in
        "help"|"-h"|"--help")
            show_help
            ;;
        "check")
            check_dependencies
            ;;
        "clean")
            clean_build
            ;;
        "debug")
            check_dependencies
            generate_protobuf
            build_project "Debug"
            ;;
        "release")
            check_dependencies
            generate_protobuf
            build_project "Release"
            ;;
        "test")
            check_dependencies
            generate_protobuf
            build_project "Release"
            run_tests
            ;;
        "all")
            check_dependencies
            generate_protobuf
            clean_build
            build_project "Release"
            print_success "构建完成！"
            print_info "运行测试命令："
            print_info "  cd build && make run_test"
            print_info "  cd build && make run_demo"
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi

# 🌟 Fiber + RPC + Raft 完美协同集成测试

## 🚀 概述

这是一个完美展示 **Fiber协程**、**RPC通信** 和 **Raft共识** 三大核心模块协同工作的综合集成测试。该测试全面验证了分布式键值存储系统的各项功能和性能。

## 🎯 核心特性

### 🧵 Fiber协程模块
- ✅ 高并发协程调度，支持数千个并发客户端
- ✅ 异步IO操作，提升网络通信效率  
- ✅ 协程间通信Channel，实现生产者-消费者模式
- ✅ 智能协程池管理，动态调整资源

### 🌐 RPC通信模块
- ✅ 基于protobuf的高效序列化
- ✅ 支持同步/异步RPC调用
- ✅ 自动重连和故障转移
- ✅ 请求负载均衡和超时控制

### 🔄 Raft共识模块
- ✅ 分布式强一致性保证
- ✅ 自动领导者选举和故障恢复
- ✅ 日志复制和快照机制
- ✅ 动态集群成员变更

## 🧪 测试场景

### 1. 基本功能测试
- **Get/Put/Append操作验证**
- **数据一致性检查**
- **错误处理测试**

### 2. 高并发协程测试
- **50个并发客户端**
- **每客户端10个操作**
- **协程调度性能验证**

### 3. 故障恢复测试
- **节点宕机模拟**
- **领导者重新选举**
- **数据恢复验证**

### 4. 性能基准测试
- **100个性能测试客户端**
- **每客户端20个操作**
- **QPS和延迟统计**

### 5. 长期稳定性测试
- **7x24小时压力测试**
- **内存泄漏检测**
- **系统稳定性验证**

## 🛠️ 编译和运行

### 编译集成测试
```bash
# 编译集成测试
cd tests
g++ -std=c++17 -o integration_test integration_test.cpp \
    -I../include \
    -L../lib \
    -lraft-kv-fiber \
    -lraft-kv-rpc \
    -lraft-kv-raft \
    -lprotobuf \
    -lpthread

# 编译客户端示例
g++ -std=c++17 -o kv_client_example kv_client_example.cpp \
    -I../include \
    -L../lib \
    -lraft-kv-rpc \
    -lprotobuf \
    -lpthread
```

### 运行集成测试
```bash
# 基本测试（3节点，60秒稳定性测试）
./integration_test

# 自定义节点数和测试时长
./integration_test 5 120

# 启用性能测试
./integration_test 3 60 perf

# 启用故障测试
./integration_test 5 60 perf fault

# 完整测试套件
./integration_test 5 300 perf fault
```

### 运行客户端示例
```bash
# 确保集群已启动，然后运行客户端示例
./kv_client_example
```

## 📊 性能指标

### 预期性能表现
- **QPS**: > 10,000 操作/秒
- **延迟**: < 1ms 平均延迟
- **并发**: 支持1000+并发客户端
- **可用性**: 99.9% 系统可用性
- **一致性**: 强一致性保证

### 监控指标
- 📊 总请求数和成功率
- ⚡ 平均/最小/最大延迟
- 🚀 协程数量和RPC调用数
- 📤📥 网络流量统计
- 🔄 领导者变更次数

## 🎮 使用示例

### 基本KV操作
```cpp
// 创建客户端
FiberKvClient client(cluster_nodes, "my_client");

// 异步Put操作
client.asyncPut("user:1001", "Alice", [](bool success) {
    if (success) {
        std::cout << "Put操作成功" << std::endl;
    }
});

// 异步Get操作
client.asyncGet("user:1001", [](bool success, const std::string& value) {
    if (success) {
        std::cout << "Get结果: " << value << std::endl;
    }
});

// 异步Append操作
client.asyncAppend("log:events", "Event1;", [](bool success) {
    if (success) {
        std::cout << "Append操作成功" << std::endl;
    }
});
```

### 并发操作
```cpp
// 创建多个协程并发执行
for (int i = 0; i < 100; i++) {
    auto fiber = std::make_shared<monsoon::Fiber>([&client, i]() {
        std::string key = "concurrent_key_" + std::to_string(i);
        std::string value = "value_" + std::to_string(i);
        
        client.asyncPut(key, value, [](bool success) {
            // 处理结果
        });
    });
    
    fiber->resume();
}
```

## 🔧 配置选项

### 集群配置
- **节点数量**: 1-10个节点
- **端口范围**: 25000-35000
- **网络协议**: TCP
- **序列化**: Protocol Buffers

### 测试配置
- **并发客户端数**: 可配置
- **操作类型**: Get/Put/Append
- **测试时长**: 可配置
- **故障模拟**: 可选

## 🐛 故障排除

### 常见问题
1. **端口冲突**: 修改端口范围或检查端口占用
2. **编译错误**: 检查依赖库和头文件路径
3. **连接失败**: 确保集群节点正常启动
4. **性能异常**: 检查系统资源和网络状况

### 调试技巧
- 使用 `gdb` 调试程序
- 查看日志输出定位问题
- 监控系统资源使用情况
- 检查网络连接状态

## 🏆 测试结果解读

### 成功标准
- ✅ 成功率 > 95%
- ✅ 平均延迟 < 10ms
- ✅ 无内存泄漏
- ✅ 故障恢复时间 < 5秒

### 性能分析
- 📈 QPS趋势分析
- 📊 延迟分布统计
- 🔄 故障恢复能力
- 💾 资源使用效率

## 🎉 总结

这个集成测试完美展示了Fiber、RPC、Raft三个模块的协同工作能力，验证了分布式键值存储系统的：

- 🚀 **高性能**: 支持高并发和低延迟
- 🔒 **强一致性**: Raft算法保证数据一致性  
- 🛡️ **高可用性**: 自动故障检测和恢复
- 📈 **可扩展性**: 支持动态集群扩展

通过这个测试，您可以全面了解系统的各项能力，为生产环境部署提供可靠的性能基准。
